[23:16:50 INF] Starting SOR.HttpApi.Host.
[23:16:53 INF] Registering datasource 'default' with db provider: 'Quartz.Impl.AdoJobStore.Common.DbProvider'
[23:16:53 INF] Using object serializer: Quartz.Simpl.JsonObjectSerializer, Quartz.Serialization.Json
[23:16:53 INF] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl
[23:16:53 INF] Quartz Scheduler created
[23:16:53 INF] JobFactory set to: Quartz.Simpl.MicrosoftDependencyInjectionJobFactory
[23:16:53 INF] Using thread monitor-based data access locking (synchronization).
[23:16:54 INF] Successfully validated presence of 10 schema objects
[23:16:54 INF] JobStoreTX initialized.
[23:16:54 INF] Quartz Scheduler ******** - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized
[23:16:54 INF] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10
[23:16:54 INF] Using job store 'Quartz.Impl.AdoJobStore.JobStoreTX', supports persistence: True, clustered: False
[23:16:54 INF] Adding 2 jobs, 2 triggers.
[23:16:54 INF] Replacing job: DEFAULT.AGVStatusPollingJob
[23:16:54 INF] Replacing job: DEFAULT.AgvTaskStatusPollingJob
[23:16:54 INF] Freed 0 triggers from 'acquired' / 'blocked' state.
[23:16:54 INF] Recovering 0 jobs that were in-progress at the time of the last shut-down.
[23:16:54 INF] Recovery complete.
[23:16:54 INF] Removed 0 'complete' triggers.
[23:16:54 INF] Removed 0 stale fired job entries.
[23:16:54 INF] Scheduler QuartzScheduler_$_NON_CLUSTERED started.
[23:16:55 WRN] Using an in-memory repository. Keys will not be persisted to storage.
[23:16:55 WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits.
[23:16:55 INF] Initialized all ABP modules.
[23:16:55 INF] Creating key {3b3c5857-c51d-42fa-b7a0-7eb864e1ac11} with creation date 2025-06-24 16:16:55Z, activation date 2025-06-24 16:16:55Z, and expiration date 2025-09-22 16:16:55Z.
[23:16:55 WRN] No XML encryptor configured. Key {3b3c5857-c51d-42fa-b7a0-7eb864e1ac11} may be persisted to storage in unencrypted form.
[23:16:55 INF] Initializing UI Database
[23:16:56 INF] Saving healthchecks configuration to database
[23:16:56 INF] Request starting HTTP/2 GET https://localhost:8443/swagger/index.html - null null
[23:16:56 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:16:56 INF] Application started. Press Ctrl+C to shut down.
[23:16:56 INF] Hosting environment: Production
[23:16:56 INF] Content root path: C:\inetpub\wwwroot\EMS\Backend
[23:16:56 INF] Scheduler QuartzScheduler_$_NON_CLUSTERED started.
[23:16:56 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Request starting HTTP/2 GET https://localhost:8443/swagger/swagger-ui.css - null null
[23:16:57 INF] Request starting HTTP/2 GET https://localhost:8443/swagger/index.js - null null
[23:16:57 INF] Request starting HTTP/2 GET https://localhost:8443/swagger/index.css - null null
[23:16:57 INF] Request finished HTTP/2 GET https://localhost:8443/swagger/index.html - 200 null text/html;charset=utf-8 859.7356ms
[23:16:57 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Request finished HTTP/2 GET https://localhost:8443/swagger/index.js - 200 null application/javascript;charset=utf-8 18.9049ms
[23:16:57 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
[23:16:57 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
[23:16:57 INF] Request finished HTTP/2 GET https://localhost:8443/swagger/index.css - 200 202 text/css 25.6901ms
[23:16:57 INF] Request finished HTTP/2 GET https://localhost:8443/swagger/swagger-ui.css - 200 152034 text/css 30.3311ms
[23:16:57 INF] Request starting HTTP/2 GET https://localhost:8443/swagger/v1/swagger.json - null null
[23:16:57 INF] Request starting HTTP/2 GET https://localhost:8443/swagger/favicon-32x32.png - null null
[23:16:57 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
[23:16:57 INF] Request finished HTTP/2 GET https://localhost:8443/swagger/favicon-32x32.png - 200 628 image/png 4.5758ms
[23:16:57 INF] Request finished HTTP/2 GET https://localhost:8443/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 620.851ms
[23:16:57 INF] Request starting HTTP/2 GET https://localhost:8443/api/abp/application-configuration - null null
[23:16:57 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:57 INF] Executing endpoint 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController.GetAsync (Volo.Abp.AspNetCore.Mvc)'
[23:16:57 INF] Route matched with {area = "abp", action = "Get", controller = "AbpApplicationConfiguration", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto] GetAsync(Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions) on controller Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController (Volo.Abp.AspNetCore.Mvc).
[23:16:58 ERR] An exception was thrown while deserializing the token.
Microsoft.AspNetCore.Antiforgery.AntiforgeryValidationException: The antiforgery token could not be decrypted.
 ---> System.Security.Cryptography.CryptographicException: The key {96de2020-188c-4dde-b07f-28417e0438ef} was not found in the key ring. For more information go to https://aka.ms/aspnet/dataprotectionwarning
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.UnprotectCore(Byte[] protectedData, Boolean allowOperationsOnRevokedKeys, UnprotectStatus& status)
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.Unprotect(Byte[] protectedData)
   at Microsoft.AspNetCore.Antiforgery.DefaultAntiforgeryTokenSerializer.Deserialize(String serializedToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Antiforgery.DefaultAntiforgeryTokenSerializer.Deserialize(String serializedToken)
   at Microsoft.AspNetCore.Antiforgery.DefaultAntiforgery.GetCookieTokenDoesNotThrow(HttpContext httpContext)
[23:16:58 INF] Executing ObjectResult, writing value of type 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto'.
[23:16:58 INF] Executed action Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController.GetAsync (Volo.Abp.AspNetCore.Mvc) in 644.6896ms
[23:16:58 INF] Executed endpoint 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController.GetAsync (Volo.Abp.AspNetCore.Mvc)'
[23:16:58 INF] Request finished HTTP/2 GET https://localhost:8443/api/abp/application-configuration - 200 null application/json; charset=utf-8 685.4937ms
[23:16:59 INF] Request starting HTTP/2 GET https://localhost:8443/abp/Swashbuckle/SetCsrfCookie - null null
[23:16:59 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:16:59 INF] Executing endpoint 'Volo.Abp.Swashbuckle.AbpSwashbuckleController.SetCsrfCookie (Volo.Abp.Swashbuckle)'
[23:16:59 INF] Route matched with {area = "Abp", action = "SetCsrfCookie", controller = "AbpSwashbuckle", page = ""}. Executing controller action with signature Void SetCsrfCookie() on controller Volo.Abp.Swashbuckle.AbpSwashbuckleController (Volo.Abp.Swashbuckle).
[23:16:59 INF] Executed action Volo.Abp.Swashbuckle.AbpSwashbuckleController.SetCsrfCookie (Volo.Abp.Swashbuckle) in 5.8332ms
[23:16:59 INF] Executed endpoint 'Volo.Abp.Swashbuckle.AbpSwashbuckleController.SetCsrfCookie (Volo.Abp.Swashbuckle)'
[23:16:59 INF] Request finished HTTP/2 GET https://localhost:8443/abp/Swashbuckle/SetCsrfCookie - 204 null null 11.5212ms
[23:16:59 INF] Request starting HTTP/2 GET https://localhost:8443/.well-known/openid-configuration - null null
[23:16:59 INF] The request URI matched a server endpoint: "Configuration".
[23:16:59 INF] The configuration request was successfully extracted: {}.
[23:16:59 INF] The configuration request was successfully validated.
[23:16:59 INF] The response was successfully returned as a JSON document: {
  "issuer": "https://localhost:8443/",
  "authorization_endpoint": "https://localhost:8443/connect/authorize",
  "token_endpoint": "https://localhost:8443/connect/token",
  "introspection_endpoint": "https://localhost:8443/connect/introspect",
  "end_session_endpoint": "https://localhost:8443/connect/endsession",
  "revocation_endpoint": "https://localhost:8443/connect/revocat",
  "userinfo_endpoint": "https://localhost:8443/connect/userinfo",
  "device_authorization_endpoint": "https://localhost:8443/device",
  "jwks_uri": "https://localhost:8443/.well-known/jwks",
  "grant_types_supported": [
    "authorization_code",
    "implicit",
    "password",
    "client_credentials",
    "refresh_token",
    "urn:ietf:params:oauth:grant-type:device_code"
  ],
  "response_types_supported": [
    "code",
    "code id_token",
    "code id_token token",
    "code token",
    "id_token",
    "id_token token",
    "token",
    "none"
  ],
  "response_modes_supported": [
    "query",
    "form_post",
    "fragment"
  ],
  "scopes_supported": [
    "openid",
    "offline_access",
    "email",
    "profile",
    "phone",
    "roles",
    "address",
    "SOR"
  ],
  "claims_supported": [
    "aud",
    "exp",
    "iat",
    "iss",
    "sub"
  ],
  "id_token_signing_alg_values_supported": [
    "RS256"
  ],
  "code_challenge_methods_supported": [
    "plain",
    "S256"
  ],
  "subject_types_supported": [
    "public"
  ],
  "prompt_values_supported": [
    "consent",
    "login",
    "none",
    "select_account"
  ],
  "token_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "introspection_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "revocation_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "device_authorization_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "claims_parameter_supported": false,
  "request_parameter_supported": false,
  "request_uri_parameter_supported": false,
  "tls_client_certificate_bound_access_tokens": false,
  "authorization_response_iss_parameter_supported": true
}.
[23:16:59 INF] Request finished HTTP/2 GET https://localhost:8443/.well-known/openid-configuration - 200 2383 application/json;charset=UTF-8 335.4923ms
[23:17:00 INF] AGVStatusPollingJob initialized at "2025-06-24T23:17:00.0556328+07:00"
[23:17:00 INF] AGVStatusPollingJob executed at "2025-06-24T23:17:00.0796102+07:00"
[23:17:00 INF] AgvTaskStatusPollingJob initialized at "2025-06-24T23:17:00.0855739+07:00"
[23:17:00 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:17:00 INF] AgvTaskStatusPollingJob executed at "2025-06-24T23:17:00.0969150+07:00"
[23:17:00 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:17:00 INF] Start processing HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:17:00 INF] Sending HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:17:00 INF] Received HTTP response headers after 33.675ms - 200
[23:17:00 INF] End processing HTTP request after 40.3145ms - 200
[23:17:00 WRN] Không có nhiệm vụ nào chưa hoàn thành!
[23:17:06 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:17:06 INF] Notification is sent on same window time.
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/.well-known/openid-configuration - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/.well-known/openid-configuration - 204 null null 13.2842ms
[23:17:15 INF] Request starting HTTP/2 GET https://localhost:8443/.well-known/openid-configuration - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] The request URI matched a server endpoint: "Configuration".
[23:17:15 INF] The configuration request was successfully extracted: {}.
[23:17:15 INF] The configuration request was successfully validated.
[23:17:15 INF] The response was successfully returned as a JSON document: {
  "issuer": "https://localhost:8443/",
  "authorization_endpoint": "https://localhost:8443/connect/authorize",
  "token_endpoint": "https://localhost:8443/connect/token",
  "introspection_endpoint": "https://localhost:8443/connect/introspect",
  "end_session_endpoint": "https://localhost:8443/connect/endsession",
  "revocation_endpoint": "https://localhost:8443/connect/revocat",
  "userinfo_endpoint": "https://localhost:8443/connect/userinfo",
  "device_authorization_endpoint": "https://localhost:8443/device",
  "jwks_uri": "https://localhost:8443/.well-known/jwks",
  "grant_types_supported": [
    "authorization_code",
    "implicit",
    "password",
    "client_credentials",
    "refresh_token",
    "urn:ietf:params:oauth:grant-type:device_code"
  ],
  "response_types_supported": [
    "code",
    "code id_token",
    "code id_token token",
    "code token",
    "id_token",
    "id_token token",
    "token",
    "none"
  ],
  "response_modes_supported": [
    "query",
    "form_post",
    "fragment"
  ],
  "scopes_supported": [
    "openid",
    "offline_access",
    "email",
    "profile",
    "phone",
    "roles",
    "address",
    "SOR"
  ],
  "claims_supported": [
    "aud",
    "exp",
    "iat",
    "iss",
    "sub"
  ],
  "id_token_signing_alg_values_supported": [
    "RS256"
  ],
  "code_challenge_methods_supported": [
    "plain",
    "S256"
  ],
  "subject_types_supported": [
    "public"
  ],
  "prompt_values_supported": [
    "consent",
    "login",
    "none",
    "select_account"
  ],
  "token_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "introspection_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "revocation_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "device_authorization_endpoint_auth_methods_supported": [
    "client_secret_post",
    "private_key_jwt",
    "client_secret_basic"
  ],
  "claims_parameter_supported": false,
  "request_parameter_supported": false,
  "request_uri_parameter_supported": false,
  "tls_client_certificate_bound_access_tokens": false,
  "authorization_response_iss_parameter_supported": true
}.
[23:17:15 INF] Request finished HTTP/2 GET https://localhost:8443/.well-known/openid-configuration - 200 2383 application/json;charset=UTF-8 9.4454ms
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/.well-known/jwks - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/.well-known/jwks - 204 null null 0.665ms
[23:17:15 INF] Request starting HTTP/2 GET https://localhost:8443/.well-known/jwks - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] The request URI matched a server endpoint: "JsonWebKeySet".
[23:17:15 INF] The JSON Web Key Set request was successfully extracted: {}.
[23:17:15 INF] The JSON Web Key Set request was successfully validated.
[23:17:15 INF] The response was successfully returned as a JSON document: {
  "keys": [
    {
      "kid": "52AE50ED965DA906513428E53234DD08226BB1AF",
      "use": "sig",
      "kty": "RSA",
      "alg": "RS256",
      "e": "AQAB",
      "n": "xRStKbIzWoskXuw5MWsQK_w8NP7esLuIKL7m8zaeXBMZg680Z4z0bXHoxk5MbIbAaB578v1hUQIy2qUEFX2_xpHUWFVCXzzm_WhLUy_PK_803jCcHqc8J6QB7r9DMB1Wt7Ca93extg3y4n-h1kiEpiDR5XD6ONr3GGb6EnEQjqLerxO04nJLmsbua7fb8cMPsCKEgUsIlnHz0qHdj8Db8v6hPJjCOeUfAV1P9eFPgr0xXxlKCpIg_tgFf5ZBkdDGt21JfVZZV3vX533YadWLDWE50YyT1DP34CSWbiy2fBaS4BDkBSXgvKORihSEaueWipgxWXtaXzucl1qqdghrZQ",
      "x5t": "Uq5Q7ZZdqQZRNCjlMjTdCCJrsa8",
      "x5c": [
        "MIIC7TCCAdWgAwIBAgIQE3ai5Xciz6BNGCwset5d/jANBgkqhkiG9w0BAQsFADAZMRcwFQYDVQQDDA5TT1IgT3BlbklkZGljdDAeFw0yNTA2MjQxMTQwMjNaFw0yNjA2MjQxMjAwMjNaMBkxFzAVBgNVBAMMDlNPUiBPcGVuSWRkaWN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxRStKbIzWoskXuw5MWsQK/w8NP7esLuIKL7m8zaeXBMZg680Z4z0bXHoxk5MbIbAaB578v1hUQIy2qUEFX2/xpHUWFVCXzzm/WhLUy/PK/803jCcHqc8J6QB7r9DMB1Wt7Ca93extg3y4n+h1kiEpiDR5XD6ONr3GGb6EnEQjqLerxO04nJLmsbua7fb8cMPsCKEgUsIlnHz0qHdj8Db8v6hPJjCOeUfAV1P9eFPgr0xXxlKCpIg/tgFf5ZBkdDGt21JfVZZV3vX533YadWLDWE50YyT1DP34CSWbiy2fBaS4BDkBSXgvKORihSEaueWipgxWXtaXzucl1qqdghrZQIDAQABozEwLzAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0OBBYEFCPKMDrq1c511J/a5gOzXXIQLV+PMA0GCSqGSIb3DQEBCwUAA4IBAQAURVWfNXu7lI6CpNrclTQIEITrX2eGEaPfjdwy9sOlO0KFIrKqdQacGBszCPhT8fW8RX2PpJSuB38D4F7KqLp8vTyJEJF10ciApNFpu+c38vRroPebrdTI9CF8GUfeec6ZJnSOwX2S4YOStlcB4KI+icE5dxteEILiEzqzYIv0ouqisQYbcboSUMEpt28a25NUidKgzMoZoAX0dqMp80s4qq+/jrETFSf+zEnrlbDizoCOOUYjIaK7kdaqlhvMUtfpQSCJx8P7w75M75c07zxlRn1EJgq1oV+4sRBM0kfMHrILmCt9WXxD4y1KN6+r3OVG6911MUzCkmUj2Yjqx7C2"
      ]
    }
  ]
}.
[23:17:15 INF] Request finished HTTP/2 GET https://localhost:8443/.well-known/jwks - 200 1623 application/json;charset=UTF-8 18.9109ms
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/abp/application-configuration?includeLocalizationResources=false - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/abp/application-configuration?includeLocalizationResources=false - 204 null null 5.5042ms
[23:17:15 INF] Request starting HTTP/2 GET https://localhost:8443/api/abp/application-configuration?includeLocalizationResources=false - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Executing endpoint 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController.GetAsync (Volo.Abp.AspNetCore.Mvc)'
[23:17:15 INF] Route matched with {area = "abp", action = "Get", controller = "AbpApplicationConfiguration", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto] GetAsync(Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions) on controller Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController (Volo.Abp.AspNetCore.Mvc).
[23:17:15 INF] Executing ObjectResult, writing value of type 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto'.
[23:17:15 INF] Executed action Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController.GetAsync (Volo.Abp.AspNetCore.Mvc) in 17.6055ms
[23:17:15 INF] Executed endpoint 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController.GetAsync (Volo.Abp.AspNetCore.Mvc)'
[23:17:15 INF] Request finished HTTP/2 GET https://localhost:8443/api/abp/application-configuration?includeLocalizationResources=false - 200 null application/json; charset=utf-8 19.5773ms
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/abp/application-localization?cultureName=en&onlyDynamics=false - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/abp/application-localization?cultureName=en&onlyDynamics=false - 204 null null 0.7295ms
[23:17:15 INF] Request starting HTTP/2 GET https://localhost:8443/api/abp/application-localization?cultureName=en&onlyDynamics=false - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Executing endpoint 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController.GetAsync (Volo.Abp.AspNetCore.Mvc)'
[23:17:15 INF] Route matched with {area = "abp", action = "Get", controller = "AbpApplicationLocalization", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto] GetAsync(Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto) on controller Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController (Volo.Abp.AspNetCore.Mvc).
[23:17:15 INF] Executing ObjectResult, writing value of type 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto'.
[23:17:15 INF] Executed action Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController.GetAsync (Volo.Abp.AspNetCore.Mvc) in 18.4743ms
[23:17:15 INF] Executed endpoint 'Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController.GetAsync (Volo.Abp.AspNetCore.Mvc)'
[23:17:15 INF] Request finished HTTP/2 GET https://localhost:8443/api/abp/application-localization?cultureName=en&onlyDynamics=false - 200 null application/json; charset=utf-8 23.2951ms
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/app/zone/zone-dtos - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/app/zone/zone-dtos - 204 null null 1.5854ms
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status/negotiate?negotiateVersion=1 - null null
[23:17:15 INF] Request starting HTTP/2 GET https://localhost:8443/api/app/zone/zone-dtos - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status/negotiate?negotiateVersion=1 - 204 null null 0.9176ms
[23:17:15 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status/negotiate?negotiateVersion=1 - null 0
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:17:15 INF] Executing endpoint '/signalr-agv-status/negotiate'
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Executing endpoint 'SOR.Zones.ZoneAppService.GetZoneDtosAsync (SOR.Application)'
[23:17:15 INF] Executed endpoint '/signalr-agv-status/negotiate'
[23:17:15 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status/negotiate?negotiateVersion=1 - 200 253 application/json 6.8818ms
[23:17:15 INF] Route matched with {action = "GetZoneDtos", controller = "Zone", area = "", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Collections.Generic.List`1[SOR.Zones.ZoneDto]] GetZoneDtosAsync() on controller SOR.Zones.ZoneAppService (SOR.Application).
[23:17:15 INF] Request starting HTTP/2 GET https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:17:15 INF] Executing endpoint '/signalr-agv-status'
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.9775ms
[23:17:15 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 32
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:17:15 INF] Executing endpoint '/signalr-agv-status'
[23:17:15 INF] Executed endpoint '/signalr-agv-status'
[23:17:15 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 3.1939ms
[23:17:15 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.6164ms
[23:17:15 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:17:15 INF] CORS policy execution successful.
[23:17:15 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:17:15 INF] Executing endpoint '/signalr-agv-status'
[23:17:15 INF] Executed endpoint '/signalr-agv-status'
[23:17:15 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 2.2082ms
[23:17:16 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:17:16 INF] Notification is sent on same window time.
[23:17:16 INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[SOR.Zones.ZoneDto, SOR.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[23:17:16 INF] Executed action SOR.Zones.ZoneAppService.GetZoneDtosAsync (SOR.Application) in 767.0078ms
[23:17:16 INF] Executed endpoint 'SOR.Zones.ZoneAppService.GetZoneDtosAsync (SOR.Application)'
[23:17:16 INF] Request finished HTTP/2 GET https://localhost:8443/api/app/zone/zone-dtos - 200 null application/json; charset=utf-8 778.4966ms
[23:17:16 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:17:16 INF] CORS policy execution successful.
[23:17:16 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - 204 null null 0.9896ms
[23:17:16 INF] Request starting HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:17:16 INF] CORS policy execution successful.
[23:17:16 INF] Executing endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:17:16 INF] Route matched with {action = "GetAll", controller = "AgvStatusQuery", area = "", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Collections.Generic.List`1[SOR.Hik.AGVStatusDto]] GetAllAsync(System.String) on controller SOR.Hik.AgvStatusQueryAppService (SOR.Application).
[23:17:16 INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[SOR.Hik.AGVStatusDto, SOR.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[23:17:16 INF] Executed action SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application) in 9.3941ms
[23:17:16 INF] Executed endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:17:16 INF] Request finished HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - 200 null application/json; charset=utf-8 15.3476ms
[23:17:26 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:17:26 INF] Notification is sent on same window time.
[23:17:30 INF] AGVStatusPollingJob initialized at "2025-06-24T23:17:30.0186694+07:00"
[23:17:30 INF] AGVStatusPollingJob executed at "2025-06-24T23:17:30.0188995+07:00"
[23:17:30 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:17:30 INF] Start processing HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:17:30 INF] Sending HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:17:30 INF] AgvTaskStatusPollingJob initialized at "2025-06-24T23:17:30.0311595+07:00"
[23:17:30 INF] AgvTaskStatusPollingJob executed at "2025-06-24T23:17:30.0313446+07:00"
[23:17:30 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:17:30 WRN] Không có nhiệm vụ nào chưa hoàn thành!
[23:17:30 INF] Received HTTP response headers after 18.6063ms - 200
[23:17:30 INF] End processing HTTP request after 19.0108ms - 200
[23:17:30 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:17:30 INF] CORS policy execution successful.
[23:17:30 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - 204 null null 0.9546ms
[23:17:30 INF] Request starting HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:17:30 INF] CORS policy execution successful.
[23:17:30 INF] Executing endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:17:30 INF] Route matched with {action = "GetAll", controller = "AgvStatusQuery", area = "", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Collections.Generic.List`1[SOR.Hik.AGVStatusDto]] GetAllAsync(System.String) on controller SOR.Hik.AgvStatusQueryAppService (SOR.Application).
[23:17:30 INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[SOR.Hik.AGVStatusDto, SOR.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[23:17:30 INF] Executed action SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application) in 3.0187ms
[23:17:30 INF] Executed endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:17:30 INF] Request finished HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - 200 null application/json; charset=utf-8 4.6912ms
[23:17:31 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:17:31 INF] CORS policy execution successful.
[23:17:31 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 1.2888ms
[23:17:31 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:17:31 INF] CORS policy execution successful.
[23:17:31 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:17:31 INF] Executing endpoint '/signalr-agv-status'
[23:17:31 INF] Executed endpoint '/signalr-agv-status'
[23:17:31 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.7023ms
[23:17:36 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:17:36 INF] Notification is sent on same window time.
[23:17:46 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:17:46 INF] Notification is sent on same window time.
[23:17:47 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:17:47 INF] CORS policy execution successful.
[23:17:47 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.7484ms
[23:17:47 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:17:47 INF] CORS policy execution successful.
[23:17:47 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:17:47 INF] Executing endpoint '/signalr-agv-status'
[23:17:47 INF] Executed endpoint '/signalr-agv-status'
[23:17:47 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.5847ms
[23:17:56 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:17:56 INF] Notification is sent on same window time.
[23:18:00 INF] AGVStatusPollingJob initialized at "2025-06-24T23:18:00.0193364+07:00"
[23:18:00 INF] AGVStatusPollingJob executed at "2025-06-24T23:18:00.0198159+07:00"
[23:18:00 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:18:00 INF] Start processing HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:18:00 INF] Sending HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:18:00 INF] Received HTTP response headers after 6.0318ms - 200
[23:18:00 INF] End processing HTTP request after 6.2001ms - 200
[23:18:00 INF] AgvTaskStatusPollingJob initialized at "2025-06-24T23:18:00.0352688+07:00"
[23:18:00 INF] AgvTaskStatusPollingJob executed at "2025-06-24T23:18:00.0354338+07:00"
[23:18:00 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:18:00 WRN] Không có nhiệm vụ nào chưa hoàn thành!
[23:18:00 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:18:00 INF] CORS policy execution successful.
[23:18:00 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - 204 null null 0.7346ms
[23:18:00 INF] Request starting HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:18:00 INF] CORS policy execution successful.
[23:18:00 INF] Executing endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:18:00 INF] Route matched with {action = "GetAll", controller = "AgvStatusQuery", area = "", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Collections.Generic.List`1[SOR.Hik.AGVStatusDto]] GetAllAsync(System.String) on controller SOR.Hik.AgvStatusQueryAppService (SOR.Application).
[23:18:00 INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[SOR.Hik.AGVStatusDto, SOR.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[23:18:00 INF] Executed action SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application) in 2.2787ms
[23:18:00 INF] Executed endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:18:00 INF] Request finished HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - 200 null application/json; charset=utf-8 3.7578ms
[23:18:02 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:18:02 INF] CORS policy execution successful.
[23:18:02 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.8261ms
[23:18:02 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:18:02 INF] CORS policy execution successful.
[23:18:02 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:18:02 INF] Executing endpoint '/signalr-agv-status'
[23:18:02 INF] Executed endpoint '/signalr-agv-status'
[23:18:02 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.3205ms
[23:18:06 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:18:06 INF] Notification is sent on same window time.
[23:18:16 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:18:16 INF] Notification is sent on same window time.
[23:18:18 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:18:18 INF] CORS policy execution successful.
[23:18:18 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.7708ms
[23:18:18 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:18:18 INF] CORS policy execution successful.
[23:18:18 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:18:18 INF] Executing endpoint '/signalr-agv-status'
[23:18:18 INF] Executed endpoint '/signalr-agv-status'
[23:18:18 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.462ms
[23:18:26 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:18:26 INF] Notification is sent on same window time.
[23:18:30 INF] AGVStatusPollingJob initialized at "2025-06-24T23:18:30.0176157+07:00"
[23:18:30 INF] AGVStatusPollingJob executed at "2025-06-24T23:18:30.0177724+07:00"
[23:18:30 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:18:30 INF] Start processing HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:18:30 INF] Sending HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:18:30 INF] Received HTTP response headers after 6.1566ms - 200
[23:18:30 INF] End processing HTTP request after 6.6566ms - 200
[23:18:30 INF] AgvTaskStatusPollingJob initialized at "2025-06-24T23:18:30.0305101+07:00"
[23:18:30 INF] AgvTaskStatusPollingJob executed at "2025-06-24T23:18:30.0306438+07:00"
[23:18:30 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:18:30 WRN] Không có nhiệm vụ nào chưa hoàn thành!
[23:18:30 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:18:30 INF] CORS policy execution successful.
[23:18:30 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - 204 null null 0.6699ms
[23:18:30 INF] Request starting HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:18:30 INF] CORS policy execution successful.
[23:18:30 INF] Executing endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:18:30 INF] Route matched with {action = "GetAll", controller = "AgvStatusQuery", area = "", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Collections.Generic.List`1[SOR.Hik.AGVStatusDto]] GetAllAsync(System.String) on controller SOR.Hik.AgvStatusQueryAppService (SOR.Application).
[23:18:30 INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[SOR.Hik.AGVStatusDto, SOR.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[23:18:30 INF] Executed action SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application) in 2.3652ms
[23:18:30 INF] Executed endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:18:30 INF] Request finished HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - 200 null application/json; charset=utf-8 3.7515ms
[23:18:34 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:18:34 INF] CORS policy execution successful.
[23:18:34 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.7668ms
[23:18:34 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:18:34 INF] CORS policy execution successful.
[23:18:34 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:18:34 INF] Executing endpoint '/signalr-agv-status'
[23:18:34 INF] Executed endpoint '/signalr-agv-status'
[23:18:34 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.3983ms
[23:18:36 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:18:36 INF] Notification is sent on same window time.
[23:18:46 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:18:46 INF] Notification is sent on same window time.
[23:18:50 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:18:50 INF] CORS policy execution successful.
[23:18:50 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 1.0239ms
[23:18:50 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:18:50 INF] CORS policy execution successful.
[23:18:50 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:18:50 INF] Executing endpoint '/signalr-agv-status'
[23:18:50 INF] Executed endpoint '/signalr-agv-status'
[23:18:50 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.3773ms
[23:18:56 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:18:56 INF] Notification is sent on same window time.
[23:19:00 INF] AGVStatusPollingJob initialized at "2025-06-24T23:19:00.0180504+07:00"
[23:19:00 INF] AGVStatusPollingJob executed at "2025-06-24T23:19:00.0189492+07:00"
[23:19:00 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:19:00 INF] Start processing HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:19:00 INF] Sending HTTP request POST http://*************:8488/rcms-dps/rest/queryAgvStatus
[23:19:00 INF] AgvTaskStatusPollingJob initialized at "2025-06-24T23:19:00.0312927+07:00"
[23:19:00 INF] AgvTaskStatusPollingJob executed at "2025-06-24T23:19:00.0313828+07:00"
[23:19:00 INF] HikAgvService initialized with base URL: http://*************:8488/
[23:19:00 WRN] Không có nhiệm vụ nào chưa hoàn thành!
[23:19:00 INF] Received HTTP response headers after 238.8021ms - 200
[23:19:00 INF] End processing HTTP request after 238.976ms - 200
[23:19:00 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:19:00 INF] CORS policy execution successful.
[23:19:00 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/api/app/agv-status-query?zoneId=all - 204 null null 0.5322ms
[23:19:00 INF] Request starting HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - null null
[23:19:00 INF] CORS policy execution successful.
[23:19:00 INF] Executing endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:19:00 INF] Route matched with {action = "GetAll", controller = "AgvStatusQuery", area = "", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Collections.Generic.List`1[SOR.Hik.AGVStatusDto]] GetAllAsync(System.String) on controller SOR.Hik.AgvStatusQueryAppService (SOR.Application).
[23:19:00 INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[SOR.Hik.AGVStatusDto, SOR.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[23:19:00 INF] Executed action SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application) in 2.2317ms
[23:19:00 INF] Executed endpoint 'SOR.Hik.AgvStatusQueryAppService.GetAllAsync (SOR.Application)'
[23:19:00 INF] Request finished HTTP/2 GET https://localhost:8443/api/app/agv-status-query?zoneId=all - 200 null application/json; charset=utf-8 3.9625ms
[23:19:05 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:19:05 INF] CORS policy execution successful.
[23:19:05 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.7285ms
[23:19:05 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:19:05 INF] CORS policy execution successful.
[23:19:05 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:19:05 INF] Executing endpoint '/signalr-agv-status'
[23:19:05 INF] Executed endpoint '/signalr-agv-status'
[23:19:05 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 1.2437ms
[23:19:06 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:19:06 INF] Notification is sent on same window time.
[23:19:16 ERR] GetHealthReport threw an exception when trying to get report from /health-status configured with name SOR Health Status.
System.InvalidOperationException: Could not get endpoint uri from configuration
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetEndpointUri(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 176
   at HealthChecks.UI.Core.HostedService.HealthCheckReportCollector.GetHealthReportAsync(HealthCheckConfiguration configuration) in /home/<USER>/work/AspNetCore.Diagnostics.HealthChecks/AspNetCore.Diagnostics.HealthChecks/src/HealthChecks.UI/Core/HostedService/HealthCheckReportCollector.cs:line 118
[23:19:16 INF] Notification is sent on same window time.
[23:19:20 INF] Request starting HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - null null
[23:19:20 INF] CORS policy execution successful.
[23:19:20 INF] Request finished HTTP/2 OPTIONS https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 204 null null 0.7498ms
[23:19:20 INF] Request starting HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - text/plain;charset=UTF-8 11
[23:19:20 INF] CORS policy execution successful.
[23:19:20 INF] Identity.Application was not authenticated. Failure message: Unprotect ticket failed
[23:19:20 INF] Executing endpoint '/signalr-agv-status'
[23:19:20 INF] Executed endpoint '/signalr-agv-status'
[23:19:20 INF] Request finished HTTP/2 POST https://localhost:8443/signalr-agv-status?id=RUV7OsYha3fgE9_gNzm0Ag - 200 null text/plain 3.4354ms
