{"version": 3, "sources": ["../../node_modules/tslib/tslib.es6.js", "../../projects/utils/src/lib/linked-list.ts"], "names": ["__generator", "thisArg", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "next", "verb", "throw", "return", "Symbol", "iterator", "this", "n", "v", "op", "TypeError", "call", "done", "value", "pop", "length", "push", "e", "step", "Object", "create", "__read", "o", "m", "r", "i", "ar", "error", "__spread", "arguments", "concat", "LinkedList", "size", "defineProperty", "prototype", "first", "last", "attach", "previousNode", "nextNode", "addHead", "addTail", "node", "ListNode", "previous", "attachMany", "values", "addManyHead", "addManyTail", "list", "toNodeArray", "detach", "dropTail", "dropHead", "add", "_this", "after", "params", "_i", "_a", "addAfter", "apply", "before", "addBefore", "byIndex", "position", "addByIndex", "head", "tail", "addMany", "addManyAfter", "addManyBefore", "addManyByIndex", "previousValue", "compareFn", "compare", "find", "nextValue", "get", "reduceRight", "nodes", "unshift", "map", "drop", "dropByIndex", "byV<PERSON>ue", "dropByValue", "byValueAll", "dropByValueAll", "dropMany", "count", "dropManyByIndex", "dropManyHead", "dropManyTail", "current", "undefined", "findIndex", "dropped", "Math", "max", "min", "predicate", "for<PERSON>ach", "iteratorFn", "index", "indexOf", "toArray", "array", "Array", "toString", "mapperFn", "JSON", "stringify", "join"], "mappings": "wYA6EgBA,EAAYC,EAASC,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,IAAOK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEM,KAAMC,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BV,EAAEU,OAAOC,UAAY,WAAa,OAAOC,OAAUZ,EACvJ,SAASO,EAAKM,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIlB,EAAG,MAAM,IAAImB,UAAU,mCAC3B,KAAOf,GAAG,IACN,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARgB,EAAG,GAASjB,EAAU,OAAIiB,EAAG,GAAKjB,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAEkB,KAAKnB,GAAI,GAAKA,EAAEQ,SAAWP,EAAIA,EAAEkB,KAAKnB,EAAGiB,EAAG,KAAKG,KAAM,OAAOnB,EAE3J,OADID,EAAI,EAAGC,IAAGgB,EAAK,CAAS,EAARA,EAAG,GAAQhB,EAAEoB,QACzBJ,EAAG,IACP,KAAK,EAAG,KAAK,EAAGhB,EAAIgB,EAAI,MACxB,KAAK,EAAc,OAAXd,EAAEC,QAAgB,CAAEiB,MAAOJ,EAAG,GAAIG,MAAM,GAChD,KAAK,EAAGjB,EAAEC,QAASJ,EAAIiB,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKd,EAAEI,IAAIe,MAAOnB,EAAEG,KAAKgB,MAAO,SACxC,QACI,KAAMrB,EAAIE,EAAEG,MAAML,EAAIA,EAAEsB,OAAS,GAAKtB,EAAEA,EAAEsB,OAAS,KAAkB,IAAVN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEd,EAAI,EAAG,SACjG,GAAc,IAAVc,EAAG,MAAchB,GAAMgB,EAAG,GAAKhB,EAAE,IAAMgB,EAAG,GAAKhB,EAAE,IAAM,CAAEE,EAAEC,MAAQa,EAAG,GAAI,MAC9E,GAAc,IAAVA,EAAG,IAAYd,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIgB,EAAI,MAC7D,GAAIhB,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIiB,KAAKP,GAAK,MACvDhB,EAAE,IAAIE,EAAEI,IAAIe,MAChBnB,EAAEG,KAAKgB,MAAO,SAEtBL,EAAKnB,EAAKqB,KAAKtB,EAASM,GAC1B,MAAOsB,GAAKR,EAAK,CAAC,EAAGQ,GAAIzB,EAAI,UAAeD,EAAIE,EAAI,EACtD,GAAY,EAARgB,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEI,MAAOJ,EAAG,GAAKA,EAAG,QAAK,EAAQG,MAAM,GArB9BM,CAAK,CAACX,EAAGC,MAyBhCW,OAAOC,gBAwBpBC,EAAOC,EAAGf,GACtB,IAAIgB,EAAsB,mBAAXnB,QAAyBkB,EAAElB,OAAOC,UACjD,IAAKkB,EAAG,OAAOD,EACf,IAAmBE,EAAYP,EAA3BQ,EAAIF,EAAEZ,KAAKW,GAAOI,EAAK,GAC3B,IACI,WAAc,IAANnB,GAAgBA,KAAM,MAAQiB,EAAIC,EAAEzB,QAAQY,MAAMc,EAAGV,KAAKQ,EAAEX,OAExE,MAAOc,GAASV,EAAI,CAAEU,MAAOA,WAEzB,IACQH,IAAMA,EAAEZ,OAASW,EAAIE,EAAU,SAAIF,EAAEZ,KAAKc,WAExC,GAAIR,EAAG,MAAMA,EAAEU,OAE7B,OAAOD,WAGKE,IACZ,IAAK,IAAIF,EAAK,GAAID,EAAI,EAAGA,EAAII,UAAUd,OAAQU,IAC3CC,EAAKA,EAAGI,OAAOT,EAAOQ,UAAUJ,KACpC,OAAOC,EA8CcP,OAAOC,aC5L9B,SAA4BP,GAAAP,KAAAO,MAAAA,gBAG9B,SAAAkB,IAGUzB,KAAA0B,KAAO,SAEfb,OAAAc,eAAIF,EAAAG,UAAA,OAAI,KAAR,WACE,OAAO5B,KAAK6B,uCAEdhB,OAAAc,eAAIF,EAAAG,UAAA,OAAI,KAAR,WACE,OAAO5B,KAAK8B,sCAEdjB,OAAAc,eAAIF,EAAAG,UAAA,SAAM,KAAV,WACE,OAAO5B,KAAK0B,sCAGND,EAAAG,UAAAG,OAAA,SACNxB,EACAyB,EACAC,GAEA,IAAKD,EAAc,OAAOhC,KAAKkC,QAAQ3B,GAEvC,IAAK0B,EAAU,OAAOjC,KAAKmC,QAAQ5B,GAEnC,IAAM6B,EAAO,IAAIC,EAAS9B,GAQ1B,OAPA6B,EAAKE,SAAWN,EAChBA,EAAatC,KAAO0C,EACpBA,EAAK1C,KAAOuC,EACZA,EAASK,SAAWF,EAEpBpC,KAAK0B,OAEEU,GAGDX,EAAAG,UAAAW,WAAA,SACNC,EACAR,EACAC,GAEA,IAAKO,EAAO/B,OAAQ,MAAO,GAE3B,IAAKuB,EAAc,OAAOhC,KAAKyC,YAAYD,GAE3C,IAAKP,EAAU,OAAOjC,KAAK0C,YAAYF,GAEvC,IAAMG,EAAO,IAAIlB,EASjB,OARAkB,EAAKD,YAAYF,GACjBG,EAAKd,MAAOS,SAAWN,EACvBA,EAAatC,KAAOiD,EAAKd,MACzBc,EAAKb,KAAMpC,KAAOuC,EAClBA,EAASK,SAAWK,EAAKb,KAEzB9B,KAAK0B,MAAQc,EAAO/B,OAEbkC,EAAKC,eAGNnB,EAAAG,UAAAiB,OAAA,SAAOT,GACb,OAAKA,EAAKE,SAELF,EAAK1C,MAEV0C,EAAKE,SAAS5C,KAAO0C,EAAK1C,KAC1B0C,EAAK1C,KAAK4C,SAAWF,EAAKE,SAE1BtC,KAAK0B,OAEEU,GAPgBpC,KAAK8C,WAFD9C,KAAK+C,YAYlCtB,EAAAG,UAAAoB,IAAA,SAAIzC,GAAJ,IAAA0C,EAAAjD,KACE,MAAO,CACLkD,MAAO,qBAACC,EAAA,GAAAC,EAAA,EAAAA,EAAA7B,UAAAd,OAAA2C,IAAAD,EAAAC,GAAA7B,UAAA6B,GACN,OAAAC,EAAAJ,EAAKK,UAASjD,KAAIkD,MAAAF,EAAA/B,EAAA,CAAC2B,EAAM1C,GAAU4C,KACrCK,OAAQ,qBAACL,EAAA,GAAAC,EAAA,EAAAA,EAAA7B,UAAAd,OAAA2C,IAAAD,EAAAC,GAAA7B,UAAA6B,GACP,OAAAC,EAAAJ,EAAKQ,WAAUpD,KAAIkD,MAAAF,EAAA/B,EAAA,CAAC2B,EAAM1C,GAAU4C,KACtCO,QAAS,SAACC,GAAqB,OAAAV,EAAKW,WAAWrD,EAAOoD,IACtDE,KAAM,WAAM,OAAAZ,EAAKf,QAAQ3B,IACzBuD,KAAM,WAAM,OAAAb,EAAKd,QAAQ5B,MAI7BkB,EAAAG,UAAAmC,QAAA,SAAQvB,GAAR,IAAAS,EAAAjD,KACE,MAAO,CACLkD,MAAO,qBAACC,EAAA,GAAAC,EAAA,EAAAA,EAAA7B,UAAAd,OAAA2C,IAAAD,EAAAC,GAAA7B,UAAA6B,GACN,OAAAC,EAAAJ,EAAKe,cAAa3D,KAAIkD,MAAAF,EAAA/B,EAAA,CAAC2B,EAAMT,GAAWW,KAC1CK,OAAQ,qBAACL,EAAA,GAAAC,EAAA,EAAAA,EAAA7B,UAAAd,OAAA2C,IAAAD,EAAAC,GAAA7B,UAAA6B,GACP,OAAAC,EAAAJ,EAAKgB,eAAc5D,KAAIkD,MAAAF,EAAA/B,EAAA,CAAC2B,EAAMT,GAAWW,KAC3CO,QAAS,SAACC,GAAqB,OAAAV,EAAKiB,eAAe1B,EAAQmB,IAC3DE,KAAM,WAAM,OAAAZ,EAAKR,YAAYD,IAC7BsB,KAAM,WAAM,OAAAb,EAAKP,YAAYF,MAMjCf,EAAAG,UAAA0B,SAAA,SAAS/C,EAAU4D,EAAoBC,QAAA,IAAAA,IAAAA,EAAAC,GACrC,IAAM/B,EAAWtC,KAAKsE,MAAK,SAAAlC,GAAQ,OAAAgC,EAAUhC,EAAK7B,MAAO4D,MAEzD,OAAO7B,EAAWtC,KAAK+B,OAAOxB,EAAO+B,EAAUA,EAAS5C,MAAQM,KAAKmC,QAAQ5B,IAK/EkB,EAAAG,UAAA6B,UAAA,SAAUlD,EAAUgE,EAAgBH,QAAA,IAAAA,IAAAA,EAAAC,GAClC,IAAM3E,EAAOM,KAAKsE,MAAK,SAAAlC,GAAQ,OAAAgC,EAAUhC,EAAK7B,MAAOgE,MAErD,OAAO7E,EAAOM,KAAK+B,OAAOxB,EAAOb,EAAK4C,SAAU5C,GAAQM,KAAKkC,QAAQ3B,IAGvEkB,EAAAG,UAAAgC,WAAA,SAAWrD,EAAUoD,GACnB,GAAIA,EAAW,EAAGA,GAAY3D,KAAK0B,UAC9B,GAAIiC,GAAY3D,KAAK0B,KAAM,OAAO1B,KAAKmC,QAAQ5B,GAEpD,GAAIoD,GAAY,EAAG,OAAO3D,KAAKkC,QAAQ3B,GAEvC,IAAMb,EAAOM,KAAKwE,IAAIb,GAEtB,OAAO3D,KAAK+B,OAAOxB,EAAOb,EAAK4C,SAAU5C,IAG3C+B,EAAAG,UAAAM,QAAA,SAAQ3B,GACN,IAAM6B,EAAO,IAAIC,EAAS9B,GAU1B,OARA6B,EAAK1C,KAAOM,KAAK6B,MAEb7B,KAAK6B,MAAO7B,KAAK6B,MAAMS,SAAWF,EACjCpC,KAAK8B,KAAOM,EAEjBpC,KAAK6B,MAAQO,EACbpC,KAAK0B,OAEEU,GAGTX,EAAAG,UAAAO,QAAA,SAAQ5B,GACN,IAAM6B,EAAO,IAAIC,EAAS9B,GAa1B,OAXIP,KAAK6B,OACPO,EAAKE,SAAWtC,KAAK8B,KACrB9B,KAAK8B,KAAMpC,KAAO0C,EAClBpC,KAAK8B,KAAOM,IAEZpC,KAAK6B,MAAQO,EACbpC,KAAK8B,KAAOM,GAGdpC,KAAK0B,OAEEU,GAKTX,EAAAG,UAAAoC,aAAA,SACExB,EACA2B,EACAC,QAAA,IAAAA,IAAAA,EAAAC,GAEA,IAAM/B,EAAWtC,KAAKsE,MAAK,SAAAlC,GAAQ,OAAAgC,EAAUhC,EAAK7B,MAAO4D,MAEzD,OAAO7B,EAAWtC,KAAKuC,WAAWC,EAAQF,EAAUA,EAAS5C,MAAQM,KAAK0C,YAAYF,IAKxFf,EAAAG,UAAAqC,cAAA,SACEzB,EACA+B,EACAH,QAAA,IAAAA,IAAAA,EAAAC,GAEA,IAAM3E,EAAOM,KAAKsE,MAAK,SAAAlC,GAAQ,OAAAgC,EAAUhC,EAAK7B,MAAOgE,MAErD,OAAO7E,EAAOM,KAAKuC,WAAWC,EAAQ9C,EAAK4C,SAAU5C,GAAQM,KAAKyC,YAAYD,IAGhFf,EAAAG,UAAAsC,eAAA,SAAe1B,EAAamB,GAG1B,GAFIA,EAAW,IAAGA,GAAY3D,KAAK0B,MAE/BiC,GAAY,EAAG,OAAO3D,KAAKyC,YAAYD,GAE3C,GAAImB,GAAY3D,KAAK0B,KAAM,OAAO1B,KAAK0C,YAAYF,GAEnD,IAAM9C,EAAOM,KAAKwE,IAAIb,GAEtB,OAAO3D,KAAKuC,WAAWC,EAAQ9C,EAAK4C,SAAU5C,IAGhD+B,EAAAG,UAAAa,YAAA,SAAYD,GAAZ,IAAAS,EAAAjD,KACE,OAAOwC,EAAOiC,aAA2B,SAACC,EAAOnE,GAE/C,OADAmE,EAAMC,QAAQ1B,EAAKf,QAAQ3B,IACpBmE,IACN,KAGLjD,EAAAG,UAAAc,YAAA,SAAYF,GAAZ,IAAAS,EAAAjD,KACE,OAAOwC,EAAOoC,KAAI,SAAArE,GAAS,OAAA0C,EAAKd,QAAQ5B,OAG1CkB,EAAAG,UAAAiD,KAAA,WAAA,IAAA5B,EAAAjD,KACE,MAAO,CACL0D,QAAS,SAACC,GAAqB,OAAAV,EAAK6B,YAAYnB,IAChDoB,QAAS,eAAC,IAAA5B,EAAA,GAAAC,EAAA,EAAAA,EAAA7B,UAAAd,OAAA2C,IAAAD,EAAAC,GAAA7B,UAAA6B,GACR,OAAAH,EAAK+B,YAAYzB,MAAMN,EAAME,IAC/B8B,WAAY,eAAC,IAAA9B,EAAA,GAAAC,EAAA,EAAAA,EAAA7B,UAAAd,OAAA2C,IAAAD,EAAAC,GAAA7B,UAAA6B,GACX,OAAAH,EAAKiC,eAAe3B,MAAMN,EAAME,IAClCU,KAAM,WAAM,OAAAZ,EAAKF,YACjBe,KAAM,WAAM,OAAAb,EAAKH,cAIrBrB,EAAAG,UAAAuD,SAAA,SAASC,GAAT,IAAAnC,EAAAjD,KACE,MAAO,CACL0D,QAAS,SAACC,GAAqB,OAAAV,EAAKoC,gBAAgBD,EAAOzB,IAC3DE,KAAM,WAAM,OAAAZ,EAAKqC,aAAaF,IAC9BtB,KAAM,WAAM,OAAAb,EAAKsC,aAAaH,MAIlC3D,EAAAG,UAAAkD,YAAA,SAAYnB,GACNA,EAAW,IAAGA,GAAY3D,KAAK0B,MAEnC,IAAM8D,EAAUxF,KAAKwE,IAAIb,GAEzB,OAAO6B,EAAUxF,KAAK6C,OAAO2C,QAAWC,GAK1ChE,EAAAG,UAAAoD,YAAA,SAAYzE,EAAY6D,QAAA,IAAAA,IAAAA,EAAAC,GACtB,IAAMV,EAAW3D,KAAK0F,WAAU,SAAAtD,GAAQ,OAAAgC,EAAUhC,EAAK7B,MAAOA,MAE9D,OAAOoD,EAAW,OAAI8B,EAAYzF,KAAK8E,YAAYnB,IAKrDlC,EAAAG,UAAAsD,eAAA,SAAe3E,EAAY6D,QAAA,IAAAA,IAAAA,EAAAC,GAGzB,IAFA,IAAMsB,EAAyB,GAEtBH,EAAUxF,KAAK6B,MAAO8B,EAAW,EAAG6B,EAAS7B,IAAY6B,EAAUA,EAAQ9F,KAC9E0E,EAAUoB,EAAQjF,MAAOA,IAC3BoF,EAAQjF,KAAKV,KAAK8E,YAAYnB,EAAWgC,EAAQlF,SAIrD,OAAOkF,GAGTlE,EAAAG,UAAAmB,SAAA,WACE,IAAMc,EAAO7D,KAAK6B,MAElB,GAAIgC,EAQF,OAPA7D,KAAK6B,MAAQgC,EAAKnE,KAEdM,KAAK6B,MAAO7B,KAAK6B,MAAMS,cAAWmD,EACjCzF,KAAK8B,UAAO2D,EAEjBzF,KAAK0B,OAEEmC,GAMXpC,EAAAG,UAAAkB,SAAA,WACE,IAAMgB,EAAO9D,KAAK8B,KAElB,GAAIgC,EAQF,OAPA9D,KAAK8B,KAAOgC,EAAKxB,SAEbtC,KAAK8B,KAAM9B,KAAK8B,KAAKpC,UAAO+F,EAC3BzF,KAAK6B,WAAQ4D,EAElBzF,KAAK0B,OAEEoC,GAMXrC,EAAAG,UAAAyD,gBAAA,SAAgBD,EAAezB,GAC7B,GAAIyB,GAAS,EAAG,MAAO,GAEvB,GAAIzB,EAAW,EAAGA,EAAWiC,KAAKC,IAAIlC,EAAW3D,KAAK0B,KAAM,QACvD,GAAIiC,GAAY3D,KAAK0B,KAAM,MAAO,GAEvC0D,EAAQQ,KAAKE,IAAIV,EAAOpF,KAAK0B,KAAOiC,GAIpC,IAFA,IAAMgC,EAAyB,GAExBP,KAAS,CACd,IAAMI,EAAUxF,KAAKwE,IAAIb,GACzBgC,EAAQjF,KAAKV,KAAK6C,OAAO2C,IAG3B,OAAOG,GAGTlE,EAAAG,UAAA0D,aAAA,SAAaF,GACX,GAAIA,GAAS,EAAG,MAAO,GAEvBA,EAAQQ,KAAKE,IAAIV,EAAOpF,KAAK0B,MAI7B,IAFA,IAAMiE,EAAyB,GAExBP,KAASO,EAAQhB,QAAQ3E,KAAK+C,YAErC,OAAO4C,GAGTlE,EAAAG,UAAA2D,aAAA,SAAaH,GACX,GAAIA,GAAS,EAAG,MAAO,GAEvBA,EAAQQ,KAAKE,IAAIV,EAAOpF,KAAK0B,MAI7B,IAFA,IAAMiE,EAAyB,GAExBP,KAASO,EAAQjF,KAAKV,KAAK8C,YAElC,OAAO6C,GAGTlE,EAAAG,UAAA0C,KAAA,SAAKyB,GACH,IAAK,IAAIP,EAAUxF,KAAK6B,MAAO8B,EAAW,EAAG6B,EAAS7B,IAAY6B,EAAUA,EAAQ9F,KAClF,GAAIqG,EAAUP,EAAS7B,EAAU3D,MAAO,OAAOwF,GAMnD/D,EAAAG,UAAA8D,UAAA,SAAUK,GACR,IAAK,IAAIP,EAAUxF,KAAK6B,MAAO8B,EAAW,EAAG6B,EAAS7B,IAAY6B,EAAUA,EAAQ9F,KAClF,GAAIqG,EAAUP,EAAS7B,EAAU3D,MAAO,OAAO2D,EAGjD,OAAQ,GAGVlC,EAAAG,UAAAoE,QAAA,SAAqBC,GACnB,IAAK,IAAI7D,EAAOpC,KAAK6B,MAAO8B,EAAW,EAAGvB,EAAMuB,IAAYvB,EAAOA,EAAK1C,KACtEuG,EAAW7D,EAAMuB,EAAU3D,OAI/ByB,EAAAG,UAAA4C,IAAA,SAAIb,GACF,OAAO3D,KAAKsE,MAAK,SAACjF,EAAG6G,GAAU,OAAAvC,IAAauC,MAK9CzE,EAAAG,UAAAuE,QAAA,SAAQ5F,EAAY6D,GAClB,YADkB,IAAAA,IAAAA,EAAAC,GACXrE,KAAK0F,WAAU,SAAAtD,GAAQ,OAAAgC,EAAUhC,EAAK7B,MAAOA,OAGtDkB,EAAAG,UAAAwE,QAAA,WACE,IAAMC,EAAQ,IAAIC,MAAMtG,KAAK0B,MAI7B,OAFA1B,KAAKgG,SAAQ,SAAC5D,EAAM8D,GAAU,OAACG,EAAMH,GAAU9D,EAAK7B,SAE7C8F,GAGT5E,EAAAG,UAAAgB,YAAA,WACE,IAAMyD,EAAQ,IAAIC,MAAMtG,KAAK0B,MAI7B,OAFA1B,KAAKgG,SAAQ,SAAC5D,EAAM8D,GAAU,OAACG,EAAMH,GAAU9D,KAExCiE,GAGT5E,EAAAG,UAAA2E,SAAA,SAASC,GACP,YADO,IAAAA,IAAAA,EAA4BC,KAAKC,WACjC1G,KAAKoG,UACTxB,KAAI,SAAArE,GAAS,OAAAiG,EAASjG,MACtBoG,KAAK,UAITlF,EAAAG,UAAC9B,OAAOC,UAAT,mEACWqC,EAAOpC,KAAK6B,MAAkB,0BAAGO,EACxC,CAAA,EAAMA,EAAK7B,OADiC,CAAA,EAAA,UAC5C8C,EAAA9D,+BAD0D6C,EAAOA,EAAK1C", "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) __createBinding(exports, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/* tslint:disable:no-non-null-assertion */\r\n\r\nimport compare from 'just-compare';\r\n\r\nexport class ListNode<T = any> {\r\n  next: ListNode | undefined;\r\n  previous: ListNode | undefined;\r\n  constructor(public readonly value: T) {}\r\n}\r\n\r\nexport class LinkedList<T = any> {\r\n  private first: ListNode<T> | undefined;\r\n  private last: ListNode<T> | undefined;\r\n  private size = 0;\r\n\r\n  get head(): ListNode<T> | undefined {\r\n    return this.first;\r\n  }\r\n  get tail(): ListNode<T> | undefined {\r\n    return this.last;\r\n  }\r\n  get length(): number {\r\n    return this.size;\r\n  }\r\n\r\n  private attach(\r\n    value: T,\r\n    previousNode: ListNode<T> | undefined,\r\n    nextNode: ListNode<T> | undefined,\r\n  ): ListNode<T> {\r\n    if (!previousNode) return this.addHead(value);\r\n\r\n    if (!nextNode) return this.addTail(value);\r\n\r\n    const node = new ListNode(value);\r\n    node.previous = previousNode;\r\n    previousNode.next = node;\r\n    node.next = nextNode;\r\n    nextNode.previous = node;\r\n\r\n    this.size++;\r\n\r\n    return node;\r\n  }\r\n\r\n  private attachMany(\r\n    values: T[],\r\n    previousNode: ListNode<T> | undefined,\r\n    nextNode: ListNode<T> | undefined,\r\n  ): ListNode<T>[] {\r\n    if (!values.length) return [];\r\n\r\n    if (!previousNode) return this.addManyHead(values);\r\n\r\n    if (!nextNode) return this.addManyTail(values);\r\n\r\n    const list = new LinkedList<T>();\r\n    list.addManyTail(values);\r\n    list.first!.previous = previousNode;\r\n    previousNode.next = list.first;\r\n    list.last!.next = nextNode;\r\n    nextNode.previous = list.last;\r\n\r\n    this.size += values.length;\r\n\r\n    return list.toNodeArray();\r\n  }\r\n\r\n  private detach(node: ListNode<T>) {\r\n    if (!node.previous) return this.dropHead();\r\n\r\n    if (!node.next) return this.dropTail();\r\n\r\n    node.previous.next = node.next;\r\n    node.next.previous = node.previous;\r\n\r\n    this.size--;\r\n\r\n    return node;\r\n  }\r\n\r\n  add(value: T) {\r\n    return {\r\n      after: (...params: [T] | [any, ListComparisonFn<T>]) =>\r\n        this.addAfter.call(this, value, ...params),\r\n      before: (...params: [T] | [any, ListComparisonFn<T>]) =>\r\n        this.addBefore.call(this, value, ...params),\r\n      byIndex: (position: number) => this.addByIndex(value, position),\r\n      head: () => this.addHead(value),\r\n      tail: () => this.addTail(value),\r\n    };\r\n  }\r\n\r\n  addMany(values: T[]) {\r\n    return {\r\n      after: (...params: [T] | [any, ListComparisonFn<T>]) =>\r\n        this.addManyAfter.call(this, values, ...params),\r\n      before: (...params: [T] | [any, ListComparisonFn<T>]) =>\r\n        this.addManyBefore.call(this, values, ...params),\r\n      byIndex: (position: number) => this.addManyByIndex(values, position),\r\n      head: () => this.addManyHead(values),\r\n      tail: () => this.addManyTail(values),\r\n    };\r\n  }\r\n\r\n  addAfter(value: T, previousValue: T): ListNode<T>;\r\n  addAfter(value: T, previousValue: any, compareFn: ListComparisonFn<T>): ListNode<T>;\r\n  addAfter(value: T, previousValue: any, compareFn: ListComparisonFn<T> = compare): ListNode<T> {\r\n    const previous = this.find(node => compareFn(node.value, previousValue));\r\n\r\n    return previous ? this.attach(value, previous, previous.next) : this.addTail(value);\r\n  }\r\n\r\n  addBefore(value: T, nextValue: T): ListNode<T>;\r\n  addBefore(value: T, nextValue: any, compareFn: ListComparisonFn<T>): ListNode<T>;\r\n  addBefore(value: T, nextValue: any, compareFn: ListComparisonFn<T> = compare): ListNode<T> {\r\n    const next = this.find(node => compareFn(node.value, nextValue));\r\n\r\n    return next ? this.attach(value, next.previous, next) : this.addHead(value);\r\n  }\r\n\r\n  addByIndex(value: T, position: number): ListNode<T> {\r\n    if (position < 0) position += this.size;\r\n    else if (position >= this.size) return this.addTail(value);\r\n\r\n    if (position <= 0) return this.addHead(value);\r\n\r\n    const next = this.get(position)!;\r\n\r\n    return this.attach(value, next.previous, next);\r\n  }\r\n\r\n  addHead(value: T): ListNode<T> {\r\n    const node = new ListNode(value);\r\n\r\n    node.next = this.first;\r\n\r\n    if (this.first) this.first.previous = node;\r\n    else this.last = node;\r\n\r\n    this.first = node;\r\n    this.size++;\r\n\r\n    return node;\r\n  }\r\n\r\n  addTail(value: T): ListNode<T> {\r\n    const node = new ListNode(value);\r\n\r\n    if (this.first) {\r\n      node.previous = this.last;\r\n      this.last!.next = node;\r\n      this.last = node;\r\n    } else {\r\n      this.first = node;\r\n      this.last = node;\r\n    }\r\n\r\n    this.size++;\r\n\r\n    return node;\r\n  }\r\n\r\n  addManyAfter(values: T[], previousValue: T): ListNode<T>[];\r\n  addManyAfter(values: T[], previousValue: any, compareFn: ListComparisonFn<T>): ListNode<T>[];\r\n  addManyAfter(\r\n    values: T[],\r\n    previousValue: any,\r\n    compareFn: ListComparisonFn<T> = compare,\r\n  ): ListNode<T>[] {\r\n    const previous = this.find(node => compareFn(node.value, previousValue));\r\n\r\n    return previous ? this.attachMany(values, previous, previous.next) : this.addManyTail(values);\r\n  }\r\n\r\n  addManyBefore(values: T[], nextValue: T): ListNode<T>[];\r\n  addManyBefore(values: T[], nextValue: any, compareFn: ListComparisonFn<T>): ListNode<T>[];\r\n  addManyBefore(\r\n    values: T[],\r\n    nextValue: any,\r\n    compareFn: ListComparisonFn<T> = compare,\r\n  ): ListNode<T>[] {\r\n    const next = this.find(node => compareFn(node.value, nextValue));\r\n\r\n    return next ? this.attachMany(values, next.previous, next) : this.addManyHead(values);\r\n  }\r\n\r\n  addManyByIndex(values: T[], position: number): ListNode<T>[] {\r\n    if (position < 0) position += this.size;\r\n\r\n    if (position <= 0) return this.addManyHead(values);\r\n\r\n    if (position >= this.size) return this.addManyTail(values);\r\n\r\n    const next = this.get(position)!;\r\n\r\n    return this.attachMany(values, next.previous, next);\r\n  }\r\n\r\n  addManyHead(values: T[]): ListNode<T>[] {\r\n    return values.reduceRight<ListNode<T>[]>((nodes, value) => {\r\n      nodes.unshift(this.addHead(value));\r\n      return nodes;\r\n    }, []);\r\n  }\r\n\r\n  addManyTail(values: T[]): ListNode<T>[] {\r\n    return values.map(value => this.addTail(value));\r\n  }\r\n\r\n  drop() {\r\n    return {\r\n      byIndex: (position: number) => this.dropByIndex(position),\r\n      byValue: (...params: [T] | [any, ListComparisonFn<T>]) =>\r\n        this.dropByValue.apply(this, params),\r\n      byValueAll: (...params: [T] | [any, ListComparisonFn<T>]) =>\r\n        this.dropByValueAll.apply(this, params),\r\n      head: () => this.dropHead(),\r\n      tail: () => this.dropTail(),\r\n    };\r\n  }\r\n\r\n  dropMany(count: number) {\r\n    return {\r\n      byIndex: (position: number) => this.dropManyByIndex(count, position),\r\n      head: () => this.dropManyHead(count),\r\n      tail: () => this.dropManyTail(count),\r\n    };\r\n  }\r\n\r\n  dropByIndex(position: number): ListNode<T> | undefined {\r\n    if (position < 0) position += this.size;\r\n\r\n    const current = this.get(position);\r\n\r\n    return current ? this.detach(current) : undefined;\r\n  }\r\n\r\n  dropByValue(value: T): ListNode<T> | undefined;\r\n  dropByValue(value: any, compareFn: ListComparisonFn<T>): ListNode<T> | undefined;\r\n  dropByValue(value: any, compareFn: ListComparisonFn<T> = compare): ListNode<T> | undefined {\r\n    const position = this.findIndex(node => compareFn(node.value, value));\r\n\r\n    return position < 0 ? undefined : this.dropByIndex(position);\r\n  }\r\n\r\n  dropByValueAll(value: T): ListNode<T>[];\r\n  dropByValueAll(value: any, compareFn: ListComparisonFn<T>): ListNode<T>[];\r\n  dropByValueAll(value: any, compareFn: ListComparisonFn<T> = compare): ListNode<T>[] {\r\n    const dropped: ListNode<T>[] = [];\r\n\r\n    for (let current = this.first, position = 0; current; position++, current = current.next) {\r\n      if (compareFn(current.value, value)) {\r\n        dropped.push(this.dropByIndex(position - dropped.length)!);\r\n      }\r\n    }\r\n\r\n    return dropped;\r\n  }\r\n\r\n  dropHead(): ListNode<T> | undefined {\r\n    const head = this.first;\r\n\r\n    if (head) {\r\n      this.first = head.next;\r\n\r\n      if (this.first) this.first.previous = undefined;\r\n      else this.last = undefined;\r\n\r\n      this.size--;\r\n\r\n      return head;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  dropTail(): ListNode<T> | undefined {\r\n    const tail = this.last;\r\n\r\n    if (tail) {\r\n      this.last = tail.previous;\r\n\r\n      if (this.last) this.last.next = undefined;\r\n      else this.first = undefined;\r\n\r\n      this.size--;\r\n\r\n      return tail;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  dropManyByIndex(count: number, position: number): ListNode<T>[] {\r\n    if (count <= 0) return [];\r\n\r\n    if (position < 0) position = Math.max(position + this.size, 0);\r\n    else if (position >= this.size) return [];\r\n\r\n    count = Math.min(count, this.size - position);\r\n\r\n    const dropped: ListNode<T>[] = [];\r\n\r\n    while (count--) {\r\n      const current = this.get(position);\r\n      dropped.push(this.detach(current!)!);\r\n    }\r\n\r\n    return dropped;\r\n  }\r\n\r\n  dropManyHead(count: Exclude<number, 0>): ListNode<T>[] {\r\n    if (count <= 0) return [];\r\n\r\n    count = Math.min(count, this.size);\r\n\r\n    const dropped: ListNode<T>[] = [];\r\n\r\n    while (count--) dropped.unshift(this.dropHead()!);\r\n\r\n    return dropped;\r\n  }\r\n\r\n  dropManyTail(count: Exclude<number, 0>): ListNode<T>[] {\r\n    if (count <= 0) return [];\r\n\r\n    count = Math.min(count, this.size);\r\n\r\n    const dropped: ListNode<T>[] = [];\r\n\r\n    while (count--) dropped.push(this.dropTail()!);\r\n\r\n    return dropped;\r\n  }\r\n\r\n  find(predicate: ListIteratorFn<T>): ListNode<T> | undefined {\r\n    for (let current = this.first, position = 0; current; position++, current = current.next) {\r\n      if (predicate(current, position, this)) return current;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  findIndex(predicate: ListIteratorFn<T>): number {\r\n    for (let current = this.first, position = 0; current; position++, current = current.next) {\r\n      if (predicate(current, position, this)) return position;\r\n    }\r\n\r\n    return -1;\r\n  }\r\n\r\n  forEach<R = boolean>(iteratorFn: ListIteratorFn<T, R>) {\r\n    for (let node = this.first, position = 0; node; position++, node = node.next) {\r\n      iteratorFn(node, position, this);\r\n    }\r\n  }\r\n\r\n  get(position: number): ListNode<T> | undefined {\r\n    return this.find((_, index) => position === index);\r\n  }\r\n\r\n  indexOf(value: T): number;\r\n  indexOf(value: any, compareFn: ListComparisonFn<T>): number;\r\n  indexOf(value: any, compareFn: ListComparisonFn<T> = compare): number {\r\n    return this.findIndex(node => compareFn(node.value, value));\r\n  }\r\n\r\n  toArray(): T[] {\r\n    const array = new Array(this.size);\r\n\r\n    this.forEach((node, index) => (array[index!] = node.value));\r\n\r\n    return array;\r\n  }\r\n\r\n  toNodeArray(): ListNode<T>[] {\r\n    const array = new Array(this.size);\r\n\r\n    this.forEach((node, index) => (array[index!] = node));\r\n\r\n    return array;\r\n  }\r\n\r\n  toString(mapperFn: ListMapperFn<T> = JSON.stringify): string {\r\n    return this.toArray()\r\n      .map(value => mapperFn(value))\r\n      .join(' <-> ');\r\n  }\r\n\r\n  // Cannot use Generator type because of ng-packagr\r\n  *[Symbol.iterator](): any {\r\n    for (let node = this.first, position = 0; node; position++, node = node.next) {\r\n      yield node.value;\r\n    }\r\n  }\r\n}\r\n\r\nexport type ListMapperFn<T = any> = (value: T) => any;\r\n\r\nexport type ListComparisonFn<T = any> = (value1: T, value2: any) => boolean;\r\n\r\nexport type ListIteratorFn<T = any, R = boolean> = (\r\n  node: ListNode<T>,\r\n  index?: number,\r\n  list?: LinkedList,\r\n) => R;\r\n"]}